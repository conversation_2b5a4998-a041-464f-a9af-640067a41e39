<?php
// Get statistics for dashboard
require '../koneksi.php';

// Get user statistics
$total_users_query = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM petugas WHERE level='proyek'");
$total_users = mysqli_fetch_array($total_users_query)['total'];

// Get recent activities (if tables exist)
$recent_activities = [];
$activities_query = mysqli_query($koneksi, "SELECT * FROM petugas WHERE level='proyek' ORDER BY id_petugas DESC LIMIT 5");
if ($activities_query) {
    while ($activity = mysqli_fetch_array($activities_query)) {
        $recent_activities[] = $activity;
    }
}
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard Admin</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0 bg-transparent p-0">
            <li class="breadcrumb-item active">Dashboard</li>
        </ol>
    </nav>
</div>

<!-- Welcome Alert -->
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-user-shield mr-2"></i>
    <strong>Selamat Datang, <?php echo htmlspecialchars($_SESSION['nama']); ?>!</strong> 
    Anda login sebagai Administrator sistem Antosa Arsitek.
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Total Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total User Proyek
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_users; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Sessions Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Sesi Aktif
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">1</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Status Sistem
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">Online</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Last Login Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Login Terakhir
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800"><?php echo date('d M Y'); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Content Row -->
<div class="row">

    <!-- Quick Actions Card -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt mr-2"></i>Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6 mb-3">
                        <a href="admin.php?url=kelola_user_proyek" class="btn btn-primary btn-block">
                            <i class="fas fa-users mr-2"></i>Kelola User Proyek
                        </a>
                    </div>
                    <div class="col-sm-6 mb-3">
                        <a href="#" class="btn btn-success btn-block">
                            <i class="fas fa-plus mr-2"></i>Tambah User Baru
                        </a>
                    </div>
                    <div class="col-sm-6 mb-3">
                        <a href="#" class="btn btn-info btn-block">
                            <i class="fas fa-chart-bar mr-2"></i>Lihat Laporan
                        </a>
                    </div>
                    <div class="col-sm-6 mb-3">
                        <a href="#" class="btn btn-warning btn-block">
                            <i class="fas fa-cogs mr-2"></i>Pengaturan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities Card -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history mr-2"></i>User Terdaftar Terbaru
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_activities)): ?>
                    <?php foreach ($recent_activities as $activity): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">User ID: <?php echo $activity['id_petugas']; ?></div>
                            <div class="font-weight-bold"><?php echo htmlspecialchars($activity['nama_petugas']); ?></div>
                            <div class="small text-muted">Username: <?php echo htmlspecialchars($activity['username']); ?></div>
                        </div>
                        <div>
                            <span class="badge badge-success">Aktif</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">Belum ada user yang terdaftar</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

</div>

<!-- System Information Row -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle mr-2"></i>Informasi Sistem
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Versi Sistem:</strong><br>
                        <span class="text-muted">v1.0.0</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Database:</strong><br>
                        <span class="text-muted">MySQL</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Server:</strong><br>
                        <span class="text-muted"><?php echo $_SERVER['SERVER_NAME']; ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong>Waktu Server:</strong><br>
                        <span class="text-muted"><?php echo date('d M Y H:i:s'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
